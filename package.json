{"name": "migaku-assessment-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/fonts": "0.11.4", "@nuxt/image": "1.10.0", "@nuxtjs/tailwindcss": "7.0.0-beta.0", "@vite-pwa/nuxt": "^1.0.1", "nuxt": "3.17.3", "typescript": "^5.6.3", "vue": "^3.5.15", "vue-router": "^4.5.1"}}