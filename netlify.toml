[build]
  command = "bun run build"
  publish = "dist"

# Redirect rules for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers for PWA
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# PWA specific headers
[[headers]]
  for = "/site.webmanifest"
  [headers.values]
    Content-Type = "application/manifest+json"

[[headers]]
  for = "/assets/*"
  [headers.values]
    cache-control = '''
    max-age=31536000,
    immutable
    '''

# Service worker caching
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache"
