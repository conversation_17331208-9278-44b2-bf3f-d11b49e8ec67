# Nuxt Minimal Starter

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

**Migaku UI Test Prompt**
For the take-home test, we've prepared a figma design file for you, you can find all the needed files and an explanation of the task below.

**Design File**
Here you can find the master design, font values etc: https://www.figma.com/design/nrKW1zzOcZBRJabYwP9rnH/Migaku-FE-Test-B-(Nov-2023)?node-id=0-1&t=1cphmQXAMkxwMqkD-1

**UI Preview**
Here you can see a demonstration of how the UI is supposed to function (button push states, transitions, etc. in action here: https://www.figma.com/proto/nrKW1zzOcZBRJabYwP9rnH/Migaku-FE-Test-B-(Nov-2023)?page-id=0%3A1&node-id=108-14021&node-type=canvas&viewport=345%2C160%2C0.37&t=SsJU41wIRNQLfqPI-1&scaling=scale-down&content-scaling=fixed&starting-point-node-id=108%3A14021

**Our expectations for this UI test**

1. Building the functionality shown above accurately according to the design specifications. Please don't just copy & paste the values for shadows, gradients, etc. from Figma to CSS but compare and adjust visually to make sure they look exactly like in the design!
2. Colors, font-sizes, paddings and margins should be as close to a pixel perfect representation as possible of the original design. If you ever feel the need to diverge from the original design it should be clearly communicated to us why you feel a divergence is necessary
3. The only operating system and browser you have to optimize for is iOS Safari. The Figma prototype is optimized for iPhone 13 mini size in portrait orientation. Please use your intuition to make it look good for iPhone 15 portrait orientation as well, as you would when designing a responsive web application
4. We should be able to add it to our iPhone's home screen to use it as a full screen web app, in which case no browser UI will be visible anymore
5. Also provide a password protected URL where we can access the final web app
6. After accepting to take this test you will have 3 days to complete it
7. Lastly, we just want to emphasize that we truly value design integrity and expect the implementation to be as close as possible to a pixel perfect representation of the design, and the animations to accurately reflect the prototype

**As the last step of the test, you should please record a video of your implementation going through the interactions. Here is a reference video of how it should look: https://spacecurve.box.com/s/**********************************

**Important note**
This test also assesses your ability to carefully read, follow, and build from a minimal set of instructions, so please understand that we won't answer any questions you have from here until test completion. You might wonder such things as "What operating system version should I consider" or "Where can I get font XYZ" or “I don’t have an iPhone”. The example answers would be "The test briefing doesn't specify it, so please decide by yourself”, "We don't supply the file here because you can find a usable font file online”, and “There’s creative solutions to that” respectively.

# We wish you the best of luck and please have fun while building!
