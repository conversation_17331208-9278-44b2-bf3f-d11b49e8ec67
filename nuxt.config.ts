// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  future: {
    compatibilityVersion: 4,
  },
  ssr: false,
  compatibilityDate: "2025-05-15",
  devtools: { enabled: true },
  modules: [
    "@nuxt/fonts",
    "@nuxt/image",
    "@vite-pwa/nuxt",
    "@nuxtjs/tailwindcss",
  ],
  experimental: {
    typedPages: true,
  },
  css: ["~/assets/main.css"],
  // Output configuration
  // nitro: {
  //   output: {
  //     dir: "dist",
  //     publicDir: "dist",
  //   },
  // },
  fonts: {
    families: [
      { name: "Inter", provider: "google" },
      {
        name: "GT Maru",
        src: "/gt-maru-black.woff",
        weight: 900,
      },
    ],
    defaults: {
      weights: [400, 500, 700, 900],
      styles: ["normal"],
    },
  },
  // Head configuration for PWA requirements
  app: {
    head: {
      charset: "utf-8",
      viewport: "width=device-width, initial-scale=1, maximum-scale=1",
      title: "Migaku Assessment App",
      meta: [
        {
          name: "description",
          content: "Assessment application optimized for iOS Safari",
        },
        { name: "theme-color", content: "#ede3ff" },
      ],
      link: [
        { rel: "icon", href: "/favicon.ico" },
        {
          rel: "apple-touch-icon",
          href: "/apple-touch-icon-180x180.png",
          sizes: "180x180",
        },
        {
          rel: "mask-icon",
          href: "/pwa-512x512.png",
          color: "#ffffff",
        },
      ],
    },
  },

  // PWA Configuration
  pwa: {
    registerType: "autoUpdate",
    manifest: {
      name: "Migaku Assessment App",
      short_name: "Migaku Assessment",
      description: "Assessment application optimized for iOS Safari",
      theme_color: "#ffffff",
      background_color: "#ffffff",
      display: "standalone",
      orientation: "portrait",
      scope: "/",
      start_url: "/",
      lang: "en",
      categories: ["education", "productivity"],
      icons: [
        {
          src: "pwa-192x192.png",
          sizes: "192x192",
          type: "image/png",
        },
        {
          src: "pwa-512x512.png",
          sizes: "512x512",
          type: "image/png",
        },
      ],
      screenshots: [
        {
          src: "screenshots-648x1392.png",
          sizes: "648x1392",
          type: "image/png",
          form_factor: "narrow",
          label: "Home screen of the assessment app",
        },
        {
          src: "screenshots-3024x1724.png",
          sizes: "3024x1724",
          type: "image/png",
          form_factor: "wide",
          label: "Desktop view of the assessment app",
        },
      ],
    },
    workbox: {
      globPatterns: ["**/*.{js,css,html,png,svg,ico}"],
    },
    client: {
      installPrompt: true,
      periodicSyncForUpdates: 20,
    },
    devOptions: {
      enabled: true,
      suppressWarnings: true,
      navigateFallback: "/",
      navigateFallbackAllowlist: [/^\/$/],
      type: "module",
    },
  },
});
