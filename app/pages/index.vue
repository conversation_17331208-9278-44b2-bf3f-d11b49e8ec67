<template>
  <div class="app-container bg-background">
    <div class="content-wrapper">
      <!-- Top section -->
      <TopSection />
      <!-- Today section -->
      <section class="today-section">
        <h2 class="text-heading-2 text-primary select-none">Today</h2>
        <div class="cards-container">
          <ReviewCard
            title="Netflix shows"
            :reviewCount="23"
            iconBgColor="#FFEBDC"
            :noKerning="true"
          >
            <template #icon>
              <SvgSaxophone />
            </template>
          </ReviewCard>
          <ReviewCard title="YouTube" :reviewCount="16" iconBgColor="#DFF4FF">
            <template #icon>
              <SvgDolphin />
            </template>
          </ReviewCard>
          <ReviewCard
            title="Prime Video"
            :reviewCount="8"
            iconBgColor="hsl(240, 100%, 18%, 0.07)"
            :disabled="true"
            iconText="P"
            iconTextColor="hsla(240, 100%, 18%, 0.8)"
          />
        </div>
      </section>
      <!-- Completed section -->
      <section class="completed-section">
        <h2 class="text-heading-3 text-primary select-none">Completed</h2>
        <div class="cards-container">
          <ReviewCard
            title="Japanese drama"
            iconBgColor="#FFE0E7"
            :completed="true"
            @click="handleCardClick"
          >
            <template #icon>
              <SvgSakura />
            </template>
          </ReviewCard>
          <ReviewCard
            title="Anime vocab"
            iconEmoji="🛵"
            iconBgColor="#F2FFA0"
            :completed="true"
            @click="handleCardClick"
          >
            <template #icon>
              <SvgBike />
            </template>
          </ReviewCard>
          <ReviewCard
            title="Kanji deck"
            iconEmoji="🌮"
            iconBgColor="#DFF4FF"
            :completed="true"
            iconText="K"
            iconTextColor="#3C91FF"
            @click="handleCardClick"
          />
        </div>
      </section>
    </div>

    <!-- Bottom Navigation -->
    <BottomNavbar />

    <!-- Completed Drawer -->
    <CompletedDrawer
      :show="showDrawer"
      :title="selectedCard?.title"
      :iconBgColor="selectedCard?.iconBgColor"
      :wordsLearned="5"
      :newCards="3"
      @close="closeDrawer"
      @study="handleStudy"
    >
      <template #icon>
        <SvgSakura v-if="selectedCard?.title === 'Japanese drama'" />
        <SvgBike v-else-if="selectedCard?.title === 'Anime vocab'" />
        <span
          v-else-if="selectedCard?.iconText"
          class="icon-text"
          :style="{ color: selectedCard.iconTextColor }"
        >
          {{ selectedCard.iconText }}
        </span>
        <span v-else>{{ selectedCard?.iconEmoji }}</span>
      </template>
    </CompletedDrawer>
  </div>
</template>

<script setup lang="ts">
interface CardProps {
  title: string;
  reviewCount?: number;
  iconEmoji?: string;
  iconText?: string;
  iconTextColor?: string;
  iconBgColor?: string;
  completed?: boolean;
  disabled?: boolean;
  noKerning?: boolean;
}

const showDrawer = ref(false);
const selectedCard = ref<CardProps | null>(null);

const handleCardClick = (cardProps: CardProps) => {
  if (cardProps.completed) {
    selectedCard.value = cardProps;
    showDrawer.value = true;
  }
};

const closeDrawer = () => {
  showDrawer.value = false;
  selectedCard.value = null;
};

const handleStudy = () => {
  // Handle study action
  closeDrawer();
};

// Scroll cards containers to the left by 1rem on mount
onMounted(() => {
  const cardsContainers = document.querySelectorAll(".cards-container");
  cardsContainers.forEach((container) => {
    // Convert 1rem to pixels (assuming 16px base font size)
    const remInPixels = parseFloat(
      getComputedStyle(document.documentElement).fontSize
    );
    container.scrollLeft = remInPixels;
  });
});
</script>

<style scoped>
.app-container {
  width: 100%;
  min-height: 100vh;
  min-height: 100dvh;
  padding-top: env(safe-area-inset-top);
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
}

.today-section {
  margin-top: var(--spacing-section-gap);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-section-gap);
}

.completed-section {
  margin-top: var(--spacing-section-gap);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-section-gap);
}

.cards-container {
  display: flex;
  gap: var(--spacing-card-gap);
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  width: 100%;
  padding: 2rem;
  margin: -2rem;
  padding-left: 2rem;

  /* Improve touch scrolling on iOS */
  touch-action: pan-x;
  /* Ensure momentum scrolling */
  -webkit-scroll-behavior: smooth;
  /* Force hardware acceleration */
  transform: translateZ(0);
  will-change: scroll-position;
}

/* Hide scrollbar for WebKit browsers */
.cards-container::-webkit-scrollbar {
  display: none;
}
</style>
