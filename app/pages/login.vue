<template>
  <div class="login-container bg-background">
    <div class="login-content">
      <!-- Migachu Logo -->
      <div class="logo-container">
        <SvgMigachu />
      </div>

      <!-- Login Card -->
      <div class="login-card bg-white shadow-elevation-1">
        <form @submit.prevent="handleLogin" class="login-form">
          <!-- Password Input -->
          <div class="input-group">
            <label
              for="password"
              class="input-label text-body-emphasis text-primary"
            >
              Password
            </label>
            <input
              id="password"
              v-model="password"
              type="password"
              class="password-input"
              placeholder="Enter password"
              required
            />
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            class="login-button bg-gradient-orange text-button-emphasis text-white"
            :disabled="isLoading"
          >
            <span v-if="!isLoading">LOGIN</span>
            <SvgMigachuSmall v-else class="button-animation" />
          </button>

          <!-- Error Message -->
          <div v-if="errorMessage" class="error-message text-caption-emphasis">
            {{ errorMessage }}
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Define page meta for authentication
definePageMeta({
  layout: false,
});

const password = ref("");
const isLoading = ref(false);
const errorMessage = ref("");

const config = useRuntimeConfig();

// Check if already authenticated
const authCookie = useCookie<string | null>("auth-token", {
  default: () => null,
  maxAge: 60 * 60 * 24 * 7, // 7 days
  secure: true,
  sameSite: "strict",
});

// Redirect if already authenticated
if (authCookie.value === "authenticated") {
  await navigateTo("/");
}

const handleLogin = async () => {
  if (!password.value) {
    errorMessage.value = "Please enter a password";
    return;
  }

  isLoading.value = true;
  errorMessage.value = "";

  // Simulate loading delay
  await new Promise((resolve) => setTimeout(resolve, 1200));

  // Check password against environment variable
  if (password.value === config.public.loginPassword) {
    // Store authentication state
    authCookie.value = "authenticated";

    // Redirect to home page
    await navigateTo("/");
  } else {
    errorMessage.value = "Incorrect password";
    isLoading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  width: 100%;
  height: 100vh;
  height: 100dvh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.login-content {
  width: 100%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 100%;
  border-radius: var(--radius-card);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-label {
  margin: 0;
}

.password-input {
  width: 100%;
  height: 44px;
  padding: 0 1rem;
  border: 1px solid var(--color-light-grey);
  border-radius: 8px;
  font-family: var(--font-inter);
  font-size: 16px;
  background-color: var(--color-white);
  color: var(--color-primary);
  -webkit-appearance: none;
  appearance: none;
}

.password-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.password-input::placeholder {
  color: hsla(240, 100%, 18%, 0.4);
}

.login-button {
  width: 100%;
  height: 40px;
  min-height: 40px;
  border: none;
  border-radius: 28px;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 150ms ease-out;
}

.login-button:active {
  transform: scale(0.91);
}

.login-button:disabled {
  cursor: not-allowed;
}

.button-animation {
  animation: spin-clockwise 1200ms ease-out 1ms;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

/* Add the same keyframe animation as used in main.css */
@keyframes spin-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  text-align: center;
  color: #ff4757;
  margin: 0;
}
</style>
