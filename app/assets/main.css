@import "tailwindcss";

/* Design System - Figma Design Tokens */
:root {
  /* Colors */
  --color-primary: #00005a;
  --color-background: #ede3ff;
  --color-white: #ffffff;
  --color-accent-green: #00c7a4;
  --color-accent-blue: #3c91ff;
  --color-accent-green-low-contrast: #ddfff9;
  --color-light-grey: hsl(240, 100%, 18%, 0.07);
  --color-white-disabled: hsla(0, 0%, 100%, 0.6);

  /* Gradients */
  --gradient-orange: linear-gradient(180deg, #ff9345 15%, #fe4670 95%);

  /* Typography */
  --font-gt-maru: "GT Maru", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --font-feature-gt-maru: "ss01" 1;
  --font-inter: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;

  /* Spacing */
  --spacing-card-gap: 1rem;
  --spacing-card-padding: 1rem;
  --spacing-section-gap: 1rem;

  /* Dimensions */
  --card-width: 152px;
  --heart-width: 169px;
  --heart-height: 158.53px;
  --progress-bar-width: 180px;
  --progress-bar-height: 40px;
  --badge-small: 24px;
  --badge-medium: 40px;
  --badge-large: 60px;

  /* Border Radius */
  --radius-card: 1rem;
  --radius-pill: 2000px;
  --radius-badge: 80px;

  /* Shadows - Progress Bar */
  --shadow-progress: 0px -0.5px 2px 0px hsl(240, 100%, 18%, 0.02),
    0px 0.5px 4px 0px hsl(240, 100%, 18%, 0.04),
    0px 3px 8px 0px hsl(240, 100%, 18%, 0.07),
    0px 8px 15px 0px hsl(240, 100%, 18%, 0.09),
    0px 16px 28px 0px hsl(240, 100%, 18%, 0.11),
    0px 48px 64px 0px hsl(240, 100%, 18%, 0.16);

  /* Shadows - Card Elevation */
  --shadow-elevation-1: 0px 0.5px 1px 0px hsl(240, 100%, 18%, 0.02),
    0px 1px 2px 0px hsl(240, 100%, 18%, 0.03),
    0px 2px 4px 0px hsl(240, 100%, 18%, 0.04),
    0px 3px 6px 0px hsl(240, 100%, 18%, 0.06),
    0px 6px 12px 0px hsl(240, 100%, 18%, 0.08),
    0px 12px 24px 0px hsl(240, 100%, 18%, 0.12);

  --shadow-elevation-1-offset: 0px -0.5px 1px 0px hsla(240, 100%, 18%, 0.02),
    0px -1px 2px 0px hsla(240, 100%, 18%, 0.03),
    0px -2px 4px 0px hsla(240, 100%, 18%, 0.04),
    0px -3px 6px 0px hsla(240, 100%, 18%, 0.06),
    0px -6px 12px 0px hsla(240, 100%, 18%, 0.08),
    0px -12px 24px 0px hsla(240, 100%, 18%, 0.12);
}

@layer base {
  /* Reset and base styles for mobile-first development */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  /* Prevent iOS text size adjustment */
  html {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
    color: var(--color-primary);
    /* Smooth scrolling with momentum on iOS */
    -webkit-overflow-scrolling: touch;
    /* Prevent overscroll bounce on iOS */
    overscroll-behavior: none;
  }

  /* Body styles optimized for mobile viewports */
  body {
    height: 100%;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Better font rendering on iOS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Default mobile-friendly font size */
    font-size: 16px;
    /* Prevent iOS zoom on form inputs */
    touch-action: manipulation;
  }

  /* Mobile-optimized typography */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    /* Prevent orphaned words on small screens */
    text-wrap: balance;
  }

  p,
  li {
    /* Better readability on mobile */
    line-height: 1.6;
    text-wrap: pretty;
  }

  /* Touch-friendly interactive elements */
  button,
  a,
  input,
  textarea,
  select {
    /* Minimum touch target size (Apple HIG recommends 44x44px) */
    min-height: 44px;
    min-width: 44px;
    /* Remove tap highlight on iOS */
    -webkit-tap-highlight-color: transparent;
  }

  /* iOS-specific button reset */
  button {
    -webkit-appearance: none;
    appearance: none;
    background: none;
    border: none;
    cursor: pointer;
  }

  /* Responsive images */
  img,
  video {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* Prevent text selection on UI elements */
  button,
  label {
    -webkit-user-select: none;
    user-select: none;
  }
}

/* Typography Utility Classes - Exact Figma Specifications */
.text-heading-2 {
  font-family: var(--font-gt-maru);
  font-weight: 900;
  font-size: 36px;
  line-height: 42px;
  letter-spacing: -0.03em;
  text-align: center;
  vertical-align: middle;
  font-feature-settings: var(--font-feature-gt-maru);
  text-rendering: optimizeLegibility;
}

.text-heading-3 {
  font-family: var(--font-gt-maru);
  font-weight: 900;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: -0.03em;
  text-align: center;
  font-feature-settings: var(--font-feature-gt-maru);
  text-rendering: optimizeLegibility;
}

.text-body-emphasis {
  font-family: var(--font-inter);
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0;
  text-align: center;
  font-kerning: auto;
  text-rendering: optimizeLegibility;
}

.text-caption-emphasis {
  font-family: var(--font-inter);
  font-weight: 700;
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0;
  text-align: center;
  text-rendering: optimizeLegibility;
}

.text-button-emphasis {
  font-family: var(--font-gt-maru);
  font-weight: 900;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: -0.03em;
  text-align: center;
  vertical-align: middle;
  text-transform: uppercase;
  font-feature-settings: var(--font-feature-gt-maru);
  text-rendering: optimizeLegibility;
}

.icon-text {
  font-family: var(--font-gt-maru);
  font-weight: 900;
  font-size: 40px;
  line-height: 100%;
  letter-spacing: -0.03em;
  text-align: center;
  vertical-align: middle;
  font-feature-settings: var(--font-feature-gt-maru);
  text-rendering: optimizeLegibility;
}

/* Color Utility Classes */
.text-primary {
  color: var(--color-primary);
}

.text-white {
  color: var(--color-white);
}

.text-accent-green {
  color: var(--color-accent-green);
}

.text-disabled {
  color: hsla(240, 100%, 18%, 0.6);
}

.text-gradient {
  background: var(--gradient-orange);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-background {
  background-color: var(--color-background);
}

.bg-white {
  background-color: var(--color-white);
}

.bg-accent-green {
  background-color: var(--color-accent-green);
}

.bg-accent-green-low-contrast {
  background-color: var(--color-accent-green-low-contrast);
}

.bg-accent-blue {
  background-color: var(--color-accent-blue);
}

.bg-disabled {
  background-color: hsl(240, 100%, 18%, 0.07);
}

.bg-gradient-orange {
  background: var(--gradient-orange);
}

/* Shadow Utility Classes */
.shadow-progress {
  box-shadow: var(--shadow-progress);
}

.shadow-elevation-1 {
  box-shadow: var(--shadow-elevation-1);
}

.shadow-elevation-1-offset {
  box-shadow: var(--shadow-elevation-1-offset);
}

/* Font kerning utility classes */
.font-kerning-none {
  font-kerning: none;
}

@keyframes spin-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-counter-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes progress-fill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width, 0%);
  }
}

.animate-progress-fill {
  animation: progress-fill 300ms ease-out forwards;
}

.animate-spin-clockwise {
  animation: spin-clockwise linear;
}

.animate-spin-counter-clockwise {
  animation: spin-counter-clockwise linear;
}
