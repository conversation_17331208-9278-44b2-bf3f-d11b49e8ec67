export default defineNuxtRouteMiddleware((to) => {
  // Skip auth check for login page
  if (to.path === "/login" || import.meta.dev) {
    return;
  }

  // Check authentication cookie
  const authCookie = useCookie<string | null>("auth-token", {
    default: () => null,
    maxAge: 60 * 60 * 24 * 7, // 7 days
    secure: true,
    sameSite: "strict",
  });

  // Redirect to login if not authenticated
  if (authCookie.value !== "authenticated") {
    return navigateTo("/login");
  }
});
