export const useAuth = () => {
  const authCookie = useCookie<string | null>('auth-token', {
    default: () => null,
    maxAge: 60 * 60 * 24 * 7, // 7 days
    secure: true,
    sameSite: 'strict',
  });

  const isAuthenticated = computed(() => authCookie.value === 'authenticated');

  const login = (password: string) => {
    const config = useRuntimeConfig();
    
    if (password === config.public.loginPassword) {
      authCookie.value = 'authenticated';
      return true;
    }
    return false;
  };

  const logout = async () => {
    authCookie.value = null;
    await navigateTo('/login');
  };

  return {
    isAuthenticated: readonly(isAuthenticated),
    login,
    logout,
  };
};
