<template>
  <nav class="bottom-navbar">
    <div class="navbar-container">
      <div>
        <SvgBook />
      </div>
      <div>
        <SvgBookStack />
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts"></script>

<style scoped>
.bottom-navbar {
  position: fixed;
  bottom: 4%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
}

.navbar-container {
  /* Auto layout */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 1rem 1.5rem;
  gap: 2rem;
  /* White */
  background: var(--color-white);
  border: 1px solid rgba(0, 0, 0, 0.03);
  /* Shadow Large */
  box-shadow: 0px 50px 65px rgba(0, 0, 90, 0.17),
    0px 15px 27.1554px rgba(0, 0, 90, 0.1),
    0px 8px 14.5186px rgba(0, 0, 90, 0.085),
    0px 3px 8.13901px rgba(0, 0, 90, 0.073),
    0px 0.381914px 4.32257px rgba(0, 0, 90, 0.04),
    0px -0.43133px 1.79872px rgba(0, 0, 90, 0.013);
  border-radius: 32px;
}
</style>
