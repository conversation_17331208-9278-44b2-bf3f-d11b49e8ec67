<template>
  <button
    class="review-card"
    :class="{
      'shadow-elevation-1': !disabled,
      clickable: !disabled,
    }"
    :disabled="disabled"
    @click="handleClick"
  >
    <div class="card-icon">
      <div class="icon-circle" :style="{ backgroundColor: iconBgColor }">
        <slot name="icon">
          <span
            v-if="iconText"
            class="icon-text"
            :style="{ color: iconTextColor }"
          >
            {{ iconText }}
          </span>
          <span v-else>{{ iconEmoji }}</span>
        </slot>
        <!-- Green check badge for completed cards -->
        <div
          v-if="completed"
          class="check-badge shadow-elevation-1 bg-accent-green"
        >
          <SvgCheck />
        </div>
      </div>
    </div>

    <h3
      class="card-title text-body-emphasis text-primary"
      :class="{ 'font-kerning-none': noKerning }"
    >
      {{ title }}
    </h3>

    <!-- Review count for regular cards -->
    <div v-if="!completed && !disabled" class="count-status bg-accent-blue">
      <span class="text-caption-emphasis text-white">
        {{ reviewCount }} reviews
      </span>
    </div>

    <!-- Paused status for disabled cards -->
    <div v-else-if="disabled" class="count-status bg-disabled text-disabled">
      <span class="text-caption-emphasis"> Paused </span>
    </div>

    <!-- Completed status for completed cards -->
    <div
      v-else
      class="count-status text-caption-emphasis bg-accent-green-low-contrast text-accent-green"
    >
      Completed
    </div>
  </button>
</template>

<script setup lang="ts">
interface Props {
  title: string;
  reviewCount?: number;
  iconEmoji?: string;
  iconText?: string;
  iconTextColor?: string;
  iconBgColor?: string;
  completed?: boolean;
  disabled?: boolean;
  noKerning?: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  click: [props: Props];
}>();

const handleClick = () => {
  if (!props.disabled) {
    emit("click", props);
  }
};
</script>

<style scoped>
.review-card {
  position: relative;
  width: var(--card-width);
  background-color: var(--color-white);
  border-radius: var(--radius-card);
  padding: var(--spacing-card-padding);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 100ms ease-in-out;
  -webkit-tap-highlight-color: transparent;
  flex-shrink: 0; /* Prevent shrinking in horizontal scroller */
  /* Improve touch responsiveness */
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;

  &:disabled {
    cursor: not-allowed;
    background-color: var(--color-white-disabled);
  }
}

.clickable {
  cursor: pointer;
}

.clickable:active {
  transform: scale(0.91);
}

.card-icon {
  width: 5rem;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  border: 1px solid var(--color-light-grey);
}

.card-title {
  margin: 0;
  margin-bottom: 0.5rem;
  text-align: center;
  flex-grow: 1;
  /* Truncate with ellipsis instead of wrapping */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.count-status {
  padding: 0.25rem 0.5rem;
  border-radius: 62px;
  white-space: nowrap;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-badge {
  position: absolute;
  top: 18px;
  right: 34px;
  width: var(--badge-small);
  height: var(--badge-small);
  border-radius: 1200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Remove button defaults */
.review-card:focus {
  outline: none;
}
</style>
