<template>
  <div class="heart-container">
    <button class="heart-button" aria-label="heart-button">
      <!-- Back heart layer -->
      <div class="heart-back">
        <SvgHeartBack />
      </div>

      <!-- Front heart layer with character -->
      <div class="heart-front">
        <SvgHeartFront />
      </div>
    </button>
    <div class="planet-magenta-container">
      <SvgPlanetMagenta />
    </div>
    <div class="comet-container">
      <SvgComet />
    </div>
    <div class="earth-container">
      <SvgEarth />
    </div>
    <div class="planet-blue-container">
      <SvgPlanetBlue />
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped>
.heart-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-section-gap);
  margin-top: 131px;
  height: 158px; /* Accommodate both hearts with offset */
  width: 158px; /* Accommodate both hearts with offset */
}

.heart-button {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  height: 158px; /* Accommodate both hearts with offset */
  width: 158px; /* Accommodate both hearts with offset */
}

.heart-back,
.heart-front,
.planet-magenta-container,
.comet-container,
.earth-container,
.planet-blue-container {
  position: absolute;
}

.planet-magenta-container {
  top: -55px;
  left: -105px;
  /* pointer-events: none; */
}

.comet-container {
  top: -59px;
  left: 174px;
  /* pointer-events: none; */
}

.earth-container {
  top: 89px;
  left: -100px;
  /* rotate: 180deg; */
  /* pointer-events: none; */
}

.planet-blue-container {
  top: 48px;
  left: 120px;
  /* pointer-events: none; */
}

.heart-back {
  z-index: 1;
  top: 13px; /* Position back heart 13px down */
  left: 0; /* Position back heart at left */
}

.heart-front {
  z-index: 2;
  top: -0.5px; /* Front heart 0.5px higher */
  left: 12.5px; /* Front heart 0.5px more to the left */
  transition: transform 150ms ease-in-out;
}

/* When pressed, front heart moves down and left to align with back heart */
.heart-button:active .heart-front {
  transform: translate(-12.5px, 13.5px);
}

/* Remove default button styles */
.heart-button:focus {
  outline: none;
}

.heart-button:active {
  outline: none;
}
</style>
