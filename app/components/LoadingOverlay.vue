<template>
  <Teleport to="body">
    <div class="loading-overlay bg-gradient-orange">
      <!-- Outer spinner with text -->
      <div
        class="outer-spinner animate-spin-clockwise"
        :style="{ animationDuration: '3000ms', animationDelay: '1ms' }"
      >
        <SvgOuterLoader />
      </div>

      <!-- Center Migachu -->
      <div
        class="migachu-container animate-spin-counter-clockwise"
        :style="{ animationDuration: '3000ms', animationDelay: '1ms' }"
      >
        <SvgMigachu />
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  show?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const emit = defineEmits<{
  close: [];
}>();

// Auto-close after 1300ms when shown
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      setTimeout(() => {
        emit("close");
      }, 1300);
    }
  }
);
</script>

<style scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-orange);
}

.outer-spinner {
  position: absolute;
}

.migachu-container {
  position: relative;
  z-index: 1;
}

/* Ensure the SVG maintains its size */
.migachu-container :deep(svg) {
  width: 72px;
  height: 72px;
}
</style>
