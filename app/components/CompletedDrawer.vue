<template>
  <Teleport to="body">
    <!-- Overlay - fades in/out -->
    <div
      class="drawer-overlay"
      :class="{ 'overlay-show': show }"
      @click="handleBackdropClick"
    ></div>

    <!-- Drawer Content - always in DOM for transitions -->
    <div
      class="drawer-content bg-white shadow-elevation-1-offset"
      :class="{ 'drawer-show': show }"
      @click.stop
    >
      <div class="drawer-inner">
        <!-- Handle bar -->
        <div class="drawer-handle"></div>

        <!-- Content -->
        <div class="drawer-body">
          <!-- Card Icon (same as ReviewCard) -->
          <div class="card-icon">
            <div class="icon-circle" :style="{ backgroundColor: iconBgColor }">
              <slot name="icon">
                <SvgSakura />
              </slot>
              <!-- Green check badge -->
              <div class="check-badge shadow-elevation-1 bg-accent-green">
                <SvgCheck />
              </div>
            </div>
          </div>

          <!-- Card Title (same as ReviewCard) -->
          <h3 class="card-title text-body-emphasis text-primary">
            {{ title }}
          </h3>

          <!-- Completed Status (same as ReviewCard) -->
          <div
            class="count-status text-caption-emphasis bg-accent-green-low-contrast text-accent-green"
          >
            Completed
          </div>

          <!-- Stats -->
          <div class="stats-container">
            <p class="stats-text text-primary">
              You learned {{ wordsLearned }} new words today
            </p>
          </div>

          <!-- Action button -->
          <button
            class="action-button bg-gradient-orange text-button-emphasis text-white"
            @click="handleStudyClick"
          >
            <span v-if="!showAnimation">STUDY {{ newCards }} NEW CARDS</span>
            <SvgMigachuSmall v-else class="button-animation" />
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  show?: boolean;
  title?: string;
  iconBgColor?: string;
  wordsLearned?: number;
  newCards?: number;
}

defineProps<Props>();

const emit = defineEmits<{
  close: [];
  study: [];
}>();

const showAnimation = ref(false);

const handleBackdropClick = () => {
  emit("close");
};

const handleStudyClick = () => {
  showAnimation.value = true;

  // Reset animation after 5 transitions
  setTimeout(() => {
    showAnimation.value = false;
  }, 1200); // 1000ms (5 × 200ms)
};
</script>

<style scoped>
.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  background: hsl(240, 100%, 18%, 0.17);
  z-index: 9999;
  opacity: 0;
  transition: opacity 300ms ease-out;
  pointer-events: none;
}

.overlay-show {
  opacity: 1;
  pointer-events: auto;
}

.drawer-content {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background: var(--color-white);
  border-radius: 24px 24px 0 0;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-rows: 0fr;
  overflow: hidden;
  transition: grid-template-rows 150ms ease-out;
  z-index: 10000;
}

.drawer-show {
  grid-template-rows: 1fr;
}

.drawer-inner {
  min-height: 0;
  overflow: hidden;
}

.drawer-handle {
  width: 45px;
  height: 5px;
  background: hsla(240, 100%, 18%, 0.35);
  border-radius: 6px;
  margin: 9px auto 0 auto;
}

.drawer-body {
  padding: 13px 28px 40px 28px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: calc(40px + env(safe-area-inset-bottom));
}

/* Card Icon (same as ReviewCard) */
.card-icon {
  width: 5rem;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  border: 1px solid var(--color-light-grey);
  position: relative;
}

.check-badge {
  position: absolute;
  top: 0px;
  right: -2.5px;
  width: var(--badge-small);
  height: var(--badge-small);
  border-radius: 1200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Card Title (same as ReviewCard) */
.card-title {
  margin: 0;
  margin-bottom: 0.5rem;
  text-align: center;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* Count Status (same as ReviewCard) */
.count-status {
  padding: 0.25rem 0.5rem;
  border-radius: 62px;
  white-space: nowrap;
  height: 1.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-container {
  margin-bottom: 1.5rem;
}

.stats-text {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  margin: 0;
}

.action-button {
  width: 100%;
  height: 40px;
  min-height: 40px;
  border: none;
  border-radius: 28px;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:active {
  transform: scale(0.91);
  transition: transform 150ms ease-out;
}

.button-animation {
  animation: spin-clockwise 1200ms ease-out 1ms;

  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

@keyframes spin-clockwise {
  0% {
    transform: rotate(0deg); /* Start: facing up */
  }
  20% {
    transform: rotate(90deg); /* 200ms: facing right */
  }
  40% {
    transform: rotate(180deg); /* 400ms: facing down */
  }
  60% {
    transform: rotate(270deg); /* 600ms: facing left */
  }
  80% {
    transform: rotate(360deg); /* 800ms: facing up */
  }
  100% {
    transform: rotate(450deg); /* 1000ms: facing right - FINAL */
  }
}
</style>
