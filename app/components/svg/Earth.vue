<template>
  <svg
    width="113"
    height="164"
    viewBox="0 0 113 164"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_i_108_14024)">
      <circle
        cx="70.4991"
        cy="89.3778"
        r="17.6028"
        transform="rotate(90 70.4991 89.3778)"
        fill="url(#paint0_linear_108_14024)"
      />
    </g>
    <circle
      cx="70.4991"
      cy="89.3778"
      r="16.8528"
      transform="rotate(90 70.4991 89.3778)"
      stroke="#5D0281"
      stroke-width="1.5"
    />
    <g style="mix-blend-mode: multiply">
      <path
        d="M66.6971 106.569C62.9163 105.737 59.5915 103.689 57.153 100.856C59.2491 98.071 66.2507 94.8633 68.0479 99.782C68.8286 101.919 67.6703 104.676 66.6971 106.569Z"
        fill="#D9D9D9"
      />
      <path
        d="M66.6971 106.569C62.9163 105.737 59.5915 103.689 57.153 100.856C59.2491 98.071 66.2507 94.8633 68.0479 99.782C68.8286 101.919 67.6703 104.676 66.6971 106.569Z"
        fill="url(#paint1_linear_108_14024)"
      />
      <path
        d="M80.3227 87.9601C76.0575 93.6663 71.3151 81.4958 67.7289 80.2068C61.8394 78.0898 59.46 86.3576 63.1026 89.8877C65.6 92.308 71.2843 92.7792 74.0044 95.4992C77.5584 99.0533 73.4097 104.633 80.7438 103.694C85.1991 100.5 88.1019 95.2779 88.1019 89.3778C88.1019 85.0112 86.512 81.0159 83.8794 77.9393C82.1188 81.0223 82.4616 85.0986 80.3227 87.9601Z"
        fill="url(#paint2_linear_108_14024)"
      />
    </g>
    <g
      clip-path="url(#paint3_diamond_108_14024_clip_path)"
      data-figma-skip-parse="true"
    >
      <g
        transform="matrix(-6.23874e-10 0.0142726 0.0141379 6.17985e-10 63.3634 38.4724)"
      >
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          fill="url(#paint3_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(1 -1)"
          fill="url(#paint3_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(-1 1)"
          fill="url(#paint3_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(-1)"
          fill="url(#paint3_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
      </g>
    </g>
    <path
      d="M63.7519 27.0384L63.3635 24.1999L62.975 27.0384C62.1728 32.9005 57.5133 37.4815 51.6384 38.1839L49.2256 38.4724L51.6384 38.7609C57.5133 39.4634 62.1728 44.0443 62.975 49.9065L63.3635 52.745L63.7519 49.9065C64.5541 44.0443 69.2136 39.4634 75.0885 38.7609L77.5013 38.4724L75.0885 38.1839C69.2136 37.4815 64.5541 32.9005 63.7519 27.0384Z"
      data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-1.2477480595407542e-06,&#34;m01&#34;:28.275709152221680,&#34;m02&#34;:49.225582122802734,&#34;m10&#34;:28.545146942138672,&#34;m11&#34;:1.2359704442133079e-06,&#34;m12&#34;:24.199790954589844},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
    />
    <g
      clip-path="url(#paint4_diamond_108_14024_clip_path)"
      data-figma-skip-parse="true"
    >
      <g
        transform="matrix(-3.7814e-10 0.00865082 0.00856917 3.7457e-10 18.3179 8.65076)"
      >
        <rect
          x="0"
          y="0"
          width="1173.4"
          height="1175.05"
          fill="url(#paint4_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1173.4"
          height="1175.05"
          transform="scale(1 -1)"
          fill="url(#paint4_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1173.4"
          height="1175.05"
          transform="scale(-1 1)"
          fill="url(#paint4_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1173.4"
          height="1175.05"
          transform="scale(-1)"
          fill="url(#paint4_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
      </g>
    </g>
    <path
      d="M19.3912 3.62765L18.3179 -3.74571e-07L17.2445 3.62764C16.66 5.60326 15.1027 7.14079 13.1198 7.70004L9.7487 8.6508L13.1198 9.60156C15.1027 10.1608 16.66 11.6983 17.2445 13.674L18.3179 17.3016L19.3912 13.674C19.9758 11.6983 21.5331 10.1608 23.516 9.60156L26.8871 8.6508L23.516 7.70004C21.5331 7.14079 19.9758 5.60326 19.3912 3.62765Z"
      data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-7.5627900741892518e-07,&#34;m01&#34;:17.138338088989258,&#34;m02&#34;:9.7487173080444336,&#34;m10&#34;:17.301647186279297,&#34;m11&#34;:7.4914049719154718e-07,&#34;m12&#34;:-5.9538437199080363e-05},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
    />
    <g
      clip-path="url(#paint5_diamond_108_14024_clip_path)"
      data-figma-skip-parse="true"
    >
      <g
        transform="matrix(-6.23874e-10 0.0142726 0.0141379 6.17985e-10 25.3029 84.1444)"
      >
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          fill="url(#paint5_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(1 -1)"
          fill="url(#paint5_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(-1 1)"
          fill="url(#paint5_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(-1)"
          fill="url(#paint5_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
      </g>
    </g>
    <path
      d="M25.6913 72.7105L25.3029 69.8719L24.9145 72.7105C24.1123 78.5726 19.4528 83.1536 13.5779 83.856L11.165 84.1445L13.5779 84.433C19.4528 85.1354 24.1123 89.7164 24.9145 95.5785L25.3029 98.417L25.6913 95.5785C26.4935 89.7164 31.153 85.1354 37.0279 84.433L39.4408 84.1445L37.0279 83.856C31.153 83.1536 26.4935 78.5726 25.6913 72.7105Z"
      data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-1.2477480595407542e-06,&#34;m01&#34;:28.275709152221680,&#34;m02&#34;:11.165036201477051,&#34;m10&#34;:28.545146942138672,&#34;m11&#34;:1.2359704442133079e-06,&#34;m12&#34;:69.871849060058594},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
    />
    <g
      clip-path="url(#paint6_diamond_108_14024_clip_path)"
      data-figma-skip-parse="true"
    >
      <g
        transform="matrix(-6.23874e-10 0.0142726 0.0141379 6.17985e-10 98.5568 128.865)"
      >
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          fill="url(#paint6_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(1 -1)"
          fill="url(#paint6_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(-1 1)"
          fill="url(#paint6_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1105.1"
          height="1106.1"
          transform="scale(-1)"
          fill="url(#paint6_diamond_108_14024)"
          opacity="1"
          shape-rendering="crispEdges"
        />
      </g>
    </g>
    <path
      d="M98.9452 117.431L98.5568 114.593L98.1684 117.431C97.3662 123.293 92.7067 127.874 86.8318 128.577L84.4189 128.865L86.8318 129.154C92.7067 129.856 97.3662 134.437 98.1684 140.299L98.5568 143.138L98.9452 140.299C99.7474 134.437 104.407 129.856 110.282 129.154L112.695 128.865L110.282 128.577C104.407 127.874 99.7474 123.293 98.9452 117.431Z"
      data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.46666666865348816,&#34;b&#34;:0.67843139171600342,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-1.2477480595407542e-06,&#34;m01&#34;:28.275709152221680,&#34;m02&#34;:84.41894531250,&#34;m10&#34;:28.545146942138672,&#34;m11&#34;:1.2359704442133079e-06,&#34;m12&#34;:114.59258270263672},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
    />
    <circle
      cx="96.906"
      cy="78.4052"
      r="1.903"
      transform="rotate(90 96.906 78.4052)"
      fill="#3FD5FF"
    />
    <circle
      cx="1.90303"
      cy="16.7425"
      r="1.903"
      transform="rotate(90 1.90303 16.7425)"
      fill="#3FD5FF"
    />
    <circle
      cx="27.2448"
      cy="43.3373"
      r="1.903"
      transform="rotate(90 27.2448 43.3373)"
      fill="#3FD5FF"
    />
    <circle
      cx="108.471"
      cy="161.371"
      r="1.903"
      transform="rotate(90 108.471 161.371)"
      fill="#3FD5FF"
    />
    <circle
      cx="67.5563"
      cy="144.244"
      r="1.903"
      transform="rotate(90 67.5563 144.244)"
      fill="#3FD5FF"
    />
    <circle
      cx="70.4991"
      cy="89.3778"
      r="16.8528"
      transform="rotate(90 70.4991 89.3778)"
      stroke="#5D0281"
      stroke-width="1.5"
    />
    <circle
      cx="70.4991"
      cy="89.3778"
      r="16.8528"
      transform="rotate(90 70.4991 89.3778)"
      stroke="#5D0281"
      stroke-width="1.5"
    />
    <defs>
      <filter
        id="filter0_i_108_14024"
        x="52.8964"
        y="71.775"
        width="35.2056"
        height="35.2056"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="-4" dy="-4" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.192157 0 0 0 0 0.709804 0 0 0 0 1 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_108_14024"
        />
      </filter>
      <clipPath id="paint3_diamond_108_14024_clip_path">
        <path
          d="M63.7519 27.0384L63.3635 24.1999L62.975 27.0384C62.1728 32.9005 57.5133 37.4815 51.6384 38.1839L49.2256 38.4724L51.6384 38.7609C57.5133 39.4634 62.1728 44.0443 62.975 49.9065L63.3635 52.745L63.7519 49.9065C64.5541 44.0443 69.2136 39.4634 75.0885 38.7609L77.5013 38.4724L75.0885 38.1839C69.2136 37.4815 64.5541 32.9005 63.7519 27.0384Z"
        />
      </clipPath>
      <clipPath id="paint4_diamond_108_14024_clip_path">
        <path
          d="M19.3912 3.62765L18.3179 -3.74571e-07L17.2445 3.62764C16.66 5.60326 15.1027 7.14079 13.1198 7.70004L9.7487 8.6508L13.1198 9.60156C15.1027 10.1608 16.66 11.6983 17.2445 13.674L18.3179 17.3016L19.3912 13.674C19.9758 11.6983 21.5331 10.1608 23.516 9.60156L26.8871 8.6508L23.516 7.70004C21.5331 7.14079 19.9758 5.60326 19.3912 3.62765Z"
        />
      </clipPath>
      <clipPath id="paint5_diamond_108_14024_clip_path">
        <path
          d="M25.6913 72.7105L25.3029 69.8719L24.9145 72.7105C24.1123 78.5726 19.4528 83.1536 13.5779 83.856L11.165 84.1445L13.5779 84.433C19.4528 85.1354 24.1123 89.7164 24.9145 95.5785L25.3029 98.417L25.6913 95.5785C26.4935 89.7164 31.153 85.1354 37.0279 84.433L39.4408 84.1445L37.0279 83.856C31.153 83.1536 26.4935 78.5726 25.6913 72.7105Z"
        />
      </clipPath>
      <clipPath id="paint6_diamond_108_14024_clip_path">
        <path
          d="M98.9452 117.431L98.5568 114.593L98.1684 117.431C97.3662 123.293 92.7067 127.874 86.8318 128.577L84.4189 128.865L86.8318 129.154C92.7067 129.856 97.3662 134.437 98.1684 140.299L98.5568 143.138L98.9452 140.299C99.7474 134.437 104.407 129.856 110.282 129.154L112.695 128.865L110.282 128.577C104.407 127.874 99.7474 123.293 98.9452 117.431Z"
        />
      </clipPath>
      <linearGradient
        id="paint0_linear_108_14024"
        x1="58.6055"
        y1="83.6688"
        x2="78.587"
        y2="98.8928"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#1387FF" />
        <stop offset="1" stop-color="#3FF0FF" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_108_14024"
        x1="52.0687"
        y1="88.2577"
        x2="87.7091"
        y2="100.056"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#A2E270" />
        <stop offset="1" stop-color="#0B7E55" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_108_14024"
        x1="60.8963"
        y1="84.5331"
        x2="87.0626"
        y2="94.5238"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#A2E270" />
        <stop offset="1" stop-color="#0B7E55" />
      </linearGradient>
      <linearGradient
        id="paint3_diamond_108_14024"
        x1="0"
        y1="0"
        x2="500"
        y2="500"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FF77AD" />
        <stop offset="1" stop-color="#FFA5FF" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint4_diamond_108_14024"
        x1="0"
        y1="0"
        x2="500"
        y2="500"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FF77AD" />
        <stop offset="1" stop-color="#FFA5FF" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint5_diamond_108_14024"
        x1="0"
        y1="0"
        x2="500"
        y2="500"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FF77AD" />
        <stop offset="1" stop-color="#FFA5FF" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint6_diamond_108_14024"
        x1="0"
        y1="0"
        x2="500"
        y2="500"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FF77AD" />
        <stop offset="1" stop-color="#FFA5FF" stop-opacity="0" />
      </linearGradient>
    </defs>
  </svg>
</template>

<script setup lang="ts"></script>
