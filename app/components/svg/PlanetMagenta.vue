<template>
  <svg
    width="193"
    height="135"
    viewBox="0 0 193 135"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      clip-path="url(#paint0_diamond_108_14023_clip_path)"
      data-figma-skip-parse="true"
    >
      <g transform="matrix(0.0135069 0 0 0.0135069 13.5068 18.6512)">
        <rect
          x="0"
          y="0"
          width="1074.04"
          height="1074.04"
          fill="url(#paint0_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1074.04"
          height="1074.04"
          transform="scale(1 -1)"
          fill="url(#paint0_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1074.04"
          height="1074.04"
          transform="scale(-1 1)"
          fill="url(#paint0_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1074.04"
          height="1074.04"
          transform="scale(-1)"
          fill="url(#paint0_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
      </g>
    </g>
    <path
      d="M0 18.6512C9.80703 20.6943 11.4636 22.3505 13.5067 32.1582C15.5501 22.3505 17.206 20.6943 27.0137 18.6512C17.206 16.6081 15.5501 14.952 13.5067 5.14426C11.4636 14.952 9.80703 16.6081 0 18.6512Z"
      data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.90588235855102539,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:27.013772964477539,&#34;m01&#34;:0.0,&#34;m02&#34;:-9.1790148871950805e-05,&#34;m10&#34;:0.0,&#34;m11&#34;:27.013874053955078,&#34;m12&#34;:5.1442818641662598},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
    />
    <g
      clip-path="url(#paint1_diamond_108_14023_clip_path)"
      data-figma-skip-parse="true"
    >
      <g transform="matrix(0.00836423 0 0 0.00836421 184.427 56.8848)">
        <rect
          x="0"
          y="0"
          width="1119.56"
          height="1119.56"
          fill="url(#paint1_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1119.56"
          height="1119.56"
          transform="scale(1 -1)"
          fill="url(#paint1_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1119.56"
          height="1119.56"
          transform="scale(-1 1)"
          fill="url(#paint1_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1119.56"
          height="1119.56"
          transform="scale(-1)"
          fill="url(#paint1_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
      </g>
    </g>
    <path
      d="M176.063 56.8847C182.136 58.1499 183.162 59.1757 184.427 65.249C185.692 59.1757 186.718 58.1499 192.791 56.8847C186.718 55.6195 185.692 54.5939 184.427 48.5206C183.162 54.5939 182.136 55.6195 176.063 56.8847Z"
      data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.90588235855102539,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:16.728462219238281,&#34;m01&#34;:0.0,&#34;m02&#34;:176.06256103515625,&#34;m10&#34;:0.0,&#34;m11&#34;:16.728429794311523,&#34;m12&#34;:48.520565032958984},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
    />
    <g
      clip-path="url(#paint2_diamond_108_14023_clip_path)"
      data-figma-skip-parse="true"
    >
      <g transform="matrix(0.0203749 0 0 0.0203747 113.344 114.628)">
        <rect
          x="0"
          y="0"
          width="1049.08"
          height="1049.08"
          fill="url(#paint2_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1049.08"
          height="1049.08"
          transform="scale(1 -1)"
          fill="url(#paint2_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1049.08"
          height="1049.08"
          transform="scale(-1 1)"
          fill="url(#paint2_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
        <rect
          x="0"
          y="0"
          width="1049.08"
          height="1049.08"
          transform="scale(-1)"
          fill="url(#paint2_diamond_108_14023)"
          opacity="1"
          shape-rendering="crispEdges"
        />
      </g>
    </g>
    <path
      d="M92.9688 114.628C107.764 117.71 110.262 120.209 113.344 135.003C116.426 120.209 118.924 117.71 133.719 114.628C118.924 111.546 116.426 109.048 113.344 94.2533C110.262 109.048 107.764 111.546 92.9688 114.628Z"
      data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.90588235855102539,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:40.749885559082031,&#34;m01&#34;:0.0,&#34;m02&#34;:92.968612670898438,&#34;m10&#34;:0.0,&#34;m11&#34;:40.749481201171875,&#34;m12&#34;:94.253372192382812},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
    />
    <path
      d="M115.323 12.7429C118.428 12.7429 120.945 10.2258 120.945 7.12079C120.945 4.01669 118.428 1.49969 115.323 1.49969C112.219 1.49982 109.702 4.01677 109.702 7.12079C109.702 10.2257 112.219 12.7427 115.323 12.7429Z"
      fill="url(#paint3_linear_108_14023)"
      stroke="#00005A"
    />
    <path
      d="M115.323 12.7429C115.723 12.7429 116.113 12.7012 116.489 12.6218C113.356 12.0979 110.968 9.37422 110.968 6.09247C110.968 4.82192 111.328 3.6362 111.948 2.6286C110.585 3.65394 109.702 5.28417 109.702 7.12079C109.702 10.2257 112.219 12.7427 115.323 12.7429Z"
      fill="url(#paint4_linear_108_14023)"
      stroke="#00005A"
    />
    <path
      d="M121.445 7.12162C121.445 10.5025 118.704 13.2432 115.323 13.2432C111.943 13.2432 109.202 10.5025 109.202 7.12162C109.202 3.74094 111.943 1.0003 115.323 1.0003C118.704 1.0003 121.445 3.74094 121.445 7.12162Z"
      stroke="#00005A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <mask id="path-7-inside-1_108_14023" fill="white">
      <path
        d="M70.1788 51.3985C70.1788 61.1185 78.0582 68.9977 87.7775 68.9977C97.4976 68.9977 105.377 61.1185 105.377 51.3985C105.377 41.6792 97.4976 33.7998 87.7775 33.7998C78.0582 33.7998 70.1788 41.6792 70.1788 51.3985Z"
      />
    </mask>
    <path
      d="M70.1788 51.3985C70.1788 61.1185 78.0582 68.9977 87.7775 68.9977C97.4976 68.9977 105.377 61.1185 105.377 51.3985C105.377 41.6792 97.4976 33.7998 87.7775 33.7998C78.0582 33.7998 70.1788 41.6792 70.1788 51.3985Z"
      fill="url(#paint5_linear_108_14023)"
    />
    <path
      d="M71.1788 51.3985C71.1788 60.5663 78.6105 67.9977 87.7775 67.9977V69.9977C77.5059 69.9977 69.1788 61.6708 69.1788 51.3985H71.1788ZM87.7775 67.9977C96.9453 67.9977 104.377 60.5663 104.377 51.3985H106.377C106.377 61.6708 98.0498 69.9977 87.7775 69.9977V67.9977ZM104.377 51.3985C104.377 42.2315 96.9453 34.7998 87.7775 34.7998V32.7998C98.0498 32.7998 106.377 41.1269 106.377 51.3985H104.377ZM87.7775 34.7998C78.6105 34.7998 71.1788 42.2315 71.1788 51.3985H69.1788C69.1788 41.1269 77.5059 32.7998 87.7775 32.7998V34.7998Z"
      fill="#00005A"
      mask="url(#path-7-inside-1_108_14023)"
    />
    <mask id="path-9-inside-2_108_14023" fill="white">
      <path
        d="M70.1788 51.3985C70.1788 61.1185 78.0582 68.9977 87.7775 68.9977C89.8482 68.9977 91.8338 68.6377 93.6789 67.9808C92.8281 68.1069 91.9581 68.1741 91.0725 68.1741C81.353 68.1741 73.4738 60.2947 73.4738 50.5747C73.4738 42.926 78.3553 36.4191 85.1719 33.9932C76.688 35.2524 70.1788 42.5648 70.1788 51.3985Z"
      />
    </mask>
    <path
      d="M70.1788 51.3985C70.1788 61.1185 78.0582 68.9977 87.7775 68.9977C89.8482 68.9977 91.8338 68.6377 93.6789 67.9808C92.8281 68.1069 91.9581 68.1741 91.0725 68.1741C81.353 68.1741 73.4738 60.2947 73.4738 50.5747C73.4738 42.926 78.3553 36.4191 85.1719 33.9932C76.688 35.2524 70.1788 42.5648 70.1788 51.3985Z"
      fill="url(#paint6_linear_108_14023)"
    />
    <path
      d="M93.6789 67.9808L93.5322 66.9916L94.0143 68.9228L93.6789 67.9808ZM85.1719 33.9932L85.0251 33.0041L85.5072 34.9353L85.1719 33.9932ZM71.1788 51.3985C71.1788 60.5663 78.6105 67.9977 87.7775 67.9977V69.9977C77.5059 69.9977 69.1788 61.6708 69.1788 51.3985H71.1788ZM87.7775 67.9977C89.7314 67.9977 91.6037 67.6581 93.3435 67.0387L94.0143 68.9228C92.0639 69.6173 89.9649 69.9977 87.7775 69.9977V67.9977ZM93.8255 68.97C92.9278 69.103 92.0088 69.1741 91.0725 69.1741V67.1741C91.9074 67.1741 92.7285 67.1107 93.5322 66.9916L93.8255 68.97ZM91.0725 69.1741C80.8007 69.1741 72.4738 60.847 72.4738 50.5747H74.4738C74.4738 59.7424 81.9053 67.1741 91.0725 67.1741V69.1741ZM72.4738 50.5747C72.4738 42.4895 77.6343 35.6142 84.8366 33.0511L85.5072 34.9353C79.0762 37.2239 74.4738 43.3625 74.4738 50.5747H72.4738ZM85.3187 34.9824C77.3177 36.1699 71.1788 43.0678 71.1788 51.3985H69.1788C69.1788 42.0618 76.0583 34.3349 85.0251 33.0041L85.3187 34.9824Z"
      fill="#00005A"
      mask="url(#path-9-inside-2_108_14023)"
    />
    <path
      d="M105.377 51.3987C105.377 61.1183 97.4974 68.9977 87.7778 68.9977C78.058 68.9977 70.1789 61.1183 70.1789 51.3987C70.1789 41.679 78.058 33.7998 87.7778 33.7998C97.4974 33.7998 105.377 41.679 105.377 51.3987Z"
      stroke="#00005A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M71.0969 46.0616L96.3712 36.0142"
      stroke="#00005A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M100.204 39.0178L73.5831 61.3916"
      stroke="#00005A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M77.8292 65.8457L105.175 53.6228"
      stroke="#00005A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <defs>
      <clipPath id="paint0_diamond_108_14023_clip_path">
        <path
          d="M0 18.6512C9.80703 20.6943 11.4636 22.3505 13.5067 32.1582C15.5501 22.3505 17.206 20.6943 27.0137 18.6512C17.206 16.6081 15.5501 14.952 13.5067 5.14426C11.4636 14.952 9.80703 16.6081 0 18.6512Z"
        />
      </clipPath>
      <clipPath id="paint1_diamond_108_14023_clip_path">
        <path
          d="M176.063 56.8847C182.136 58.1499 183.162 59.1757 184.427 65.249C185.692 59.1757 186.718 58.1499 192.791 56.8847C186.718 55.6195 185.692 54.5939 184.427 48.5206C183.162 54.5939 182.136 55.6195 176.063 56.8847Z"
        />
      </clipPath>
      <clipPath id="paint2_diamond_108_14023_clip_path">
        <path
          d="M92.9688 114.628C107.764 117.71 110.262 120.209 113.344 135.003C116.426 120.209 118.924 117.71 133.719 114.628C118.924 111.546 116.426 109.048 113.344 94.2533C110.262 109.048 107.764 111.546 92.9688 114.628Z"
        />
      </clipPath>
      <linearGradient
        id="paint0_diamond_108_14023"
        x1="0"
        y1="0"
        x2="500"
        y2="500"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FFE700" />
        <stop offset="1" stop-color="#FFA5FF" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint1_diamond_108_14023"
        x1="0"
        y1="0"
        x2="500"
        y2="500"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FFE700" />
        <stop offset="1" stop-color="#FFA5FF" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint2_diamond_108_14023"
        x1="0"
        y1="0"
        x2="500"
        y2="500"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FFE700" />
        <stop offset="1" stop-color="#FFA5FF" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_108_14023"
        x1="109.202"
        y1="7.12163"
        x2="121.445"
        y2="7.12163"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3FFFFF" />
        <stop offset="0.508977" stop-color="#3FC9FF" />
        <stop offset="0.786159" stop-color="#3FC9FF" />
        <stop offset="1" stop-color="#006AFF" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_108_14023"
        x1="109.202"
        y1="7.19672"
        x2="118.936"
        y2="7.19672"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FFA5FF" />
        <stop offset="0.417324" stop-color="#FFE700" />
        <stop offset="1" stop-color="#FFE700" />
      </linearGradient>
      <linearGradient
        id="paint5_linear_108_14023"
        x1="70.1788"
        y1="51.3987"
        x2="105.377"
        y2="51.3987"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FF95FF" />
        <stop offset="0.390749" stop-color="#FF95FF" />
        <stop offset="1" stop-color="#5F2ABF" />
      </linearGradient>
      <linearGradient
        id="paint6_linear_108_14023"
        x1="70.1789"
        y1="51.4954"
        x2="93.6788"
        y2="51.4954"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3FFFFF" />
        <stop offset="0.508977" stop-color="#3FC9FF" />
        <stop offset="0.786159" stop-color="#3FC9FF" />
        <stop offset="1" stop-color="#006AFF" />
      </linearGradient>
    </defs>
  </svg>
</template>

<script setup lang="ts"></script>
