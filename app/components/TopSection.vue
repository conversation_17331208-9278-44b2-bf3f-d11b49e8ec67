<template>
  <button class="monkey-button shadow-elevation-1" @click="handleMonkeyClick">
    <SvgMonkey />
  </button>
  <ProgressBar :progress="progress" />
  <HeartButton />

  <!-- Logout Button -->
  <button class="logout-button text-caption-emphasis" @click="handleLogout">
    Logout
  </button>

  <!-- Loading Overlay -->
  <LoadingOverlay :show="showLoadingOverlay" @close="handleLoadingClose" />
</template>

<script setup lang="ts">
// Reactive state
const progress = ref(0);
const showLoadingOverlay = ref(false);

const { logout } = useAuth();

const handleMonkeyClick = () => {
  showLoadingOverlay.value = true;
};

const handleLoadingClose = () => {
  showLoadingOverlay.value = false;
  // Navigate to home
  navigateTo("/");
};

const handleLogout = () => {
  logout();
};
</script>

<style scoped>
.monkey-button {
  position: fixed;
  top: 51px;
  left: calc(50% - 150px);
  transform: translateX(-50%);
  border-radius: 80px;
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  z-index: 50;
  -webkit-tap-highlight-color: transparent;
}

.logout-button {
  position: fixed;
  top: 51px;
  right: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-primary);
  z-index: 50;
  -webkit-tap-highlight-color: transparent;
  padding: 0.5rem;
}
</style>
