<template>
  <button class="monkey-button shadow-elevation-1" @click="handleMonkeyClick">
    <SvgMonkey />
  </button>
  <ProgressBar :progress="progress" />
  <HeartButton />

  <!-- Loading Overlay -->
  <LoadingOverlay :show="showLoadingOverlay" @close="handleLoadingClose" />
</template>

<script setup lang="ts">
// Reactive state
const progress = ref(0);
const showLoadingOverlay = ref(false);

const handleMonkeyClick = () => {
  showLoadingOverlay.value = true;
};

const handleLoadingClose = () => {
  showLoadingOverlay.value = false;
  // Navigate to home
  navigateTo("/");
};
</script>

<style scoped>
.monkey-button {
  position: fixed;
  left: calc(50% - 142px);
  transform: translateX(-50%);
  border-radius: 80px;
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  z-index: 50;
  -webkit-tap-highlight-color: transparent;
}
</style>
