<template>
  <button class="progress-bar-container" @click="handleClick">
    <div class="progress-bar shadow-progress bg-white">
      <!-- Squiggly line SVG -->
      <div class="squiggly-container">
        <!-- Empty track - always visible underneath -->
        <SvgNoProgress class="squiggly-line squiggly-track" />

        <!-- Filled progress - animates on top -->
        <SvgFullProgress
          v-if="
            progress === 100 ||
            (isAnimating && animationDirection === 'reverse')
          "
          class="squiggly-line squiggly-fill"
          :class="{
            'animate-fill': isAnimating && animationDirection === 'forward',
            'animate-empty': isAnimating && animationDirection === 'reverse',
            'fill-static': progress === 100 && !isAnimating,
          }"
        />
      </div>

      <!-- Progress text -->
      <div class="progress-text-container">
        <span
          class="progress-text text-body-emphasis"
          :class="{
            'text-visible': progress === 0,
            'text-hidden': progress === 100,
          }"
        >
          0%
        </span>
        <span
          class="progress-text text-body-emphasis"
          :class="{
            'text-visible': progress === 100,
            'text-hidden': progress === 0,
          }"
        >
          100%
        </span>
      </div>
    </div>
  </button>
</template>

<script setup lang="ts">
import SvgNoProgress from "~/components/svg/NoProgress.vue";
import SvgFullProgress from "~/components/svg/FullProgress.vue";

const progress = ref(0);
const isAnimating = ref(false);
const animationDirection = ref<"forward" | "reverse">("forward");

const handleClick = () => {
  if (isAnimating.value) return;

  isAnimating.value = true;

  // Toggle between 0% and 100% with appropriate animation direction
  if (progress.value === 0) {
    // Forward animation: 0% → 100%
    animationDirection.value = "forward";
    progress.value = 100;
  } else {
    // Reverse animation: 100% → 0%
    animationDirection.value = "reverse";
    progress.value = 0; // Change text immediately
  }

  // Reset animation state after animation completes
  setTimeout(() => {
    isAnimating.value = false;
  }, 300); // Match the 300ms animation duration
};
</script>

<style scoped>
.progress-bar-container {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
  background: none;
  border: none;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.progress-bar {
  position: relative;
  width: var(--progress-bar-width);
  height: var(--progress-bar-height);
  background-color: var(--color-white);
  border-radius: var(--radius-pill);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 7.5px;
}

.squiggly-container {
  position: absolute;
  left: 11px;
  top: 50%;
  transform: translateY(-50%);
  width: calc(
    100% - 62.5px
  ); /* Leave space for text (44px) + right padding (7.5px) + left padding (11px) */
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
}

.squiggly-line {
  width: 112px;
  height: 12px;
  transition: all 300ms ease-out;
}

.squiggly-track {
  /* Empty track - always visible underneath */
  position: absolute;
  z-index: 1;
}

.squiggly-fill {
  /* Filled progress - on top of track */
  position: absolute;
  z-index: 2;
}

.fill-static {
  /* Static filled state - fully visible */
  clip-path: inset(0 0 0 0);
}

/* Animation for the squiggly line fill */
.animate-fill {
  animation: squiggly-fill 300ms ease-out forwards;
}

.animate-empty {
  animation: squiggly-empty 300ms ease-out forwards;
}

/* Forward animation: left to right */
@keyframes squiggly-fill {
  from {
    clip-path: inset(0 100% 0 0);
  }
  to {
    clip-path: inset(0 0 0 0);
  }
}

/* Reverse animation: right to left */
@keyframes squiggly-empty {
  from {
    clip-path: inset(0 0 0 0);
  }
  to {
    clip-path: inset(0 100% 0 0);
  }
}

.progress-text-container {
  position: relative;
  z-index: 2;
  width: 44px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  position: absolute;
  width: 44px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  /* Primary color */
  color: var(--color-primary);

  /* Smooth opacity transition */
  transition: opacity 300ms ease;
}

.text-visible {
  opacity: 1;
}

.text-hidden {
  opacity: 0;
}
</style>
